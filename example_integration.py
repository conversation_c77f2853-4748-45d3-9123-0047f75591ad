#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V<PERSON> dụ tích hợp SelectorTracker vào code automation
"""

from selector_analyzer import SelectorTracker
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import time

class OptimizedAutoChrome:
    """Ví dụ class automation với selector tracking"""
    
    def __init__(self):
        self.driver = None
        self.tracker = SelectorTracker()
    
    def __checkElement(self, by: By, value: str, timeout: int = 5):
        """Kiểm tra element có tồn tại không và log selector usage"""
        try:
            # Log selector usage
            by_name = by.name if hasattr(by, 'name') else str(by).split('.')[-1]
            self.tracker.log_selector_usage(by_name.upper(), value, "autoChrome.py")
            
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return True
        except:
            return False
    
    def find_element_with_tracking(self, by: By, value: str):
        """Find element với tracking"""
        by_name = by.name if hasattr(by, 'name') else str(by).split('.')[-1]
        self.tracker.log_selector_usage(by_name.upper(), value, "autoChrome.py")
        return self.driver.find_element(by, value)
    
    def login_tiktok_example(self, username: str, password: str):
        """Ví dụ login TikTok với selector tracking"""
        print("🔐 Bắt đầu đăng nhập TikTok...")
        
        # Các selector thường dùng trong login
        login_selectors = [
            ('XPATH', '//div[contains(@class,"login-modal")]'),
            ('XPATH', '//button[@data-e2e="login-button"]'),
            ('XPATH', '//input[@name="username"]'),
            ('XPATH', '//input[@type="password"]'),
            ('CSS', '[data-e2e="like-icon"]'),
        ]
        
        # Simulate selector usage
        for selector_type, selector in login_selectors:
            if selector_type == 'XPATH':
                by = By.XPATH
            else:
                by = By.CSS_SELECTOR
                
            # Giả lập việc sử dụng selector
            self.tracker.log_selector_usage(selector_type, selector, "login_process")
            time.sleep(0.1)  # Simulate processing time
        
        # Hiển thị thống kê session
        self.tracker.show_session_summary()
        print("✅ Hoàn thành đăng nhập!")

def demo_selector_tracking():
    """Demo selector tracking"""
    print("🎯 DEMO: Theo dõi Selector Usage trong thời gian thực")
    print("=" * 60)
    
    auto_chrome = OptimizedAutoChrome()
    
    # Giả lập quá trình automation
    print("\n1️⃣ Giả lập quá trình đăng nhập...")
    auto_chrome.login_tiktok_example("test_user", "test_pass")
    
    print("\n2️⃣ Giả lập sử dụng selector nhiều lần...")
    # Simulate heavy usage of certain selectors
    heavy_selectors = [
        ('XPATH', '//button[text()="Đăng nhập"]'),
        ('XPATH', '//div[@class="modal-content"]'),
        ('CSS', '[data-e2e="comment-input"]'),
    ]
    
    for i in range(3):
        for selector_type, selector in heavy_selectors:
            auto_chrome.tracker.log_selector_usage(selector_type, selector, f"job_{i+1}")
            time.sleep(0.05)
    
    print("\n3️⃣ Kết quả cuối cùng:")
    auto_chrome.tracker.show_session_summary()
    
    print("\n💡 Lợi ích của việc tracking:")
    print("   ✅ Phát hiện selector được sử dụng quá nhiều")
    print("   ✅ Tối ưu hóa performance bằng cách cache element")
    print("   ✅ Ưu tiên tối ưu các selector quan trọng")
    print("   ✅ Theo dõi hiệu suất automation real-time")

if __name__ == "__main__":
    demo_selector_tracking()
