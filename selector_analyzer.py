#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Selector Usage Analyzer for AutoChrome-SourceBan
Công cụ đơn giản để phân tích và ưu tiên các selector đ<PERSON><PERSON><PERSON> sử dụng nhiều nhất
"""

import os
import re
import time
from collections import Counter, defaultdict
from datetime import datetime

class SelectorAnalyzer:
    def __init__(self, project_root: str = "."):
        self.project_root = project_root
        self.selector_stats = defaultdict(int)
        self.selector_locations = defaultdict(list)
        self.file_stats = defaultdict(int)

        # Patterns để tìm các selector quan trọng
        self.patterns = {
            'XPATH': [
                r'By\.XPATH\s*,\s*[\'"]([^\'"]+)[\'"]',
                r'find_element\s*\(\s*By\.XPATH\s*,\s*[\'"]([^\'"]+)[\'"]',
                r'__checkElement\s*\(\s*By\.XPATH\s*,\s*[\'"]([^\'"]+)[\'"]',
            ],
            'CSS': [
                r'By\.CSS_SELECTOR\s*,\s*[\'"]([^\'"]+)[\'"]',
                r'find_element\s*\(\s*By\.CSS_SELECTOR\s*,\s*[\'"]([^\'"]+)[\'"]',
            ],
            'ID': [
                r'By\.ID\s*,\s*[\'"]([^\'"]+)[\'"]',
                r'getElementById\s*\(\s*[\'"]([^\'"]+)[\'"]',
            ]
        }

    def analyze_file(self, file_path: str):
        """Phân tích một file để tìm các selector"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except:
            return

        total_selectors = 0

        # Tìm tất cả các loại selector
        for selector_type, patterns in self.patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    selector = match.strip()
                    if selector and len(selector) > 2:
                        full_selector = f"{selector_type}: {selector}"
                        self.selector_stats[full_selector] += 1
                        self.selector_locations[full_selector].append(file_path)
                        total_selectors += 1

        if total_selectors > 0:
            self.file_stats[file_path] = total_selectors

    def scan_project(self):
        """Quét toàn bộ project để tìm selector"""
        print("🔍 Đang quét project...")

        python_files = []
        for root, dirs, files in os.walk(self.project_root):
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'runtools.build']]
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))

        print(f"📁 Tìm thấy {len(python_files)} file Python")

        for file_path in python_files:
            self.analyze_file(file_path)

        print("✅ Hoàn thành!")

    def show_results(self):
        """Hiển thị kết quả phân tích"""
        print("\n" + "=" * 70)
        print("📊 KẾT QUẢ PHÂN TÍCH SELECTOR")
        print("=" * 70)
        print(f"🎯 Tổng selector unique: {len(self.selector_stats)}")
        print(f"📄 File có selector: {len(self.file_stats)}")

        # Thống kê theo loại
        type_stats = defaultdict(int)
        for selector in self.selector_stats:
            selector_type = selector.split(':')[0]
            type_stats[selector_type] += self.selector_stats[selector]

        print(f"\n📊 THỐNG KÊ THEO LOẠI:")
        for sel_type, count in type_stats.items():
            print(f"   {sel_type}: {count} lần")

        # Top selector được sử dụng nhiều nhất
        print(f"\n🏆 TOP 15 SELECTOR ĐƯỢC SỬ DỤNG NHIỀU NHẤT:")
        print("-" * 70)

        top_selectors = Counter(self.selector_stats).most_common(15)
        for i, (selector, count) in enumerate(top_selectors, 1):
            files = set(self.selector_locations[selector])
            # Rút gọn selector nếu quá dài
            display_selector = selector if len(selector) <= 60 else selector[:57] + "..."
            print(f"{i:2d}. [{count:2d}x] {display_selector}")

            # Hiển thị file sử dụng
            file_names = [os.path.basename(f) for f in list(files)[:2]]
            if len(files) <= 2:
                print(f"     📁 {', '.join(file_names)}")
            else:
                print(f"     📁 {', '.join(file_names)} + {len(files)-2} file khác")

        # Gợi ý tối ưu hóa đơn giản
        print(f"\n💡 GỢI Ý TỐI ƯU HÓA:")
        xpath_count = sum(1 for s in self.selector_stats if s.startswith('XPATH:'))
        css_count = sum(1 for s in self.selector_stats if s.startswith('CSS:'))

        if xpath_count > css_count * 2:
            print("   ⚡ Nên chuyển một số XPath sang CSS selector để tăng tốc độ")

        # Tìm selector được sử dụng nhiều nhất cần ưu tiên
        priority_selectors = [s for s, count in top_selectors[:5] if count >= 3]
        if priority_selectors:
            print("   🎯 Ưu tiên tối ưu các selector được sử dụng nhiều:")
            for selector in priority_selectors[:3]:
                count = self.selector_stats[selector]
                print(f"      - [{count}x] {selector[:50]}...")

        print("=" * 70)

class SelectorTracker:
    """Theo dõi selector usage trong thời gian thực"""
    def __init__(self):
        self.usage_log = []
        self.session_stats = defaultdict(int)

    def log_selector_usage(self, selector_type: str, selector: str, file_name: str = "runtime"):
        """Ghi log khi selector được sử dụng"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_selector = f"{selector_type}: {selector}"

        self.usage_log.append({
            'time': timestamp,
            'selector': full_selector,
            'file': file_name
        })
        self.session_stats[full_selector] += 1

        # Hiển thị thông tin nếu selector được sử dụng nhiều
        if self.session_stats[full_selector] >= 3:
            print(f"⚠️  [{timestamp}] Selector được sử dụng {self.session_stats[full_selector]} lần: {selector[:40]}...")

    def show_session_summary(self):
        """Hiển thị tóm tắt session"""
        if not self.usage_log:
            print("📊 Chưa có selector nào được sử dụng trong session này")
            return

        print(f"\n📊 TÓM TẮT SESSION ({len(self.usage_log)} lần sử dụng):")
        top_session = Counter(self.session_stats).most_common(5)
        for i, (selector, count) in enumerate(top_session, 1):
            print(f"  {i}. [{count}x] {selector[:50]}...")

def main():
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--track":
        # Chế độ theo dõi thời gian thực
        print("🔍 Chế độ theo dõi Selector Usage")
        print("Tích hợp vào code automation để theo dõi...")
        tracker = SelectorTracker()

        # Ví dụ sử dụng
        print("\n💡 Cách sử dụng trong code:")
        print("from selector_analyzer import SelectorTracker")
        print("tracker = SelectorTracker()")
        print('tracker.log_selector_usage("XPATH", "//button[@data-e2e=\\"login-button\\"]")')
        print("tracker.show_session_summary()")

    else:
        # Chế độ phân tích static
        print("🚀 Phân tích Selector Usage - AutoChrome-SourceBan")
        print("=" * 50)

        analyzer = SelectorAnalyzer(".")

        # Quét project
        start_time = time.time()
        analyzer.scan_project()
        end_time = time.time()

        print(f"⏱️  Thời gian quét: {end_time - start_time:.1f} giây")

        # Hiển thị kết quả
        analyzer.show_results()

        print(f"\n💡 Để theo dõi thời gian thực: python {sys.argv[0]} --track")

if __name__ == "__main__":
    main()
