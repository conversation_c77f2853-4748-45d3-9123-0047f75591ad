#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Selector Usage Analyzer for AutoChrome-SourceBan
Phân tích và thống kê các selector (XPath, CSS) được sử dụng nhiều nhất
"""

import os
import re
import json
import time
from collections import Counter, defaultdict
from datetime import datetime
from typing import Dict, List, Tuple, Set
import argparse

class SelectorAnalyzer:
    def __init__(self, project_root: str = "."):
        self.project_root = project_root
        self.selector_stats = defaultdict(int)
        self.selector_locations = defaultdict(list)
        self.file_stats = defaultdict(int)
        self.selector_types = defaultdict(int)
        
        # Patterns để tìm các selector
        self.xpath_patterns = [
            r'By\.XPATH\s*,\s*[\'"]([^\'"]+)[\'"]',
            r'find_element\s*\(\s*By\.XPATH\s*,\s*[\'"]([^\'"]+)[\'"]',
            r'find_elements\s*\(\s*By\.XPATH\s*,\s*[\'"]([^\'"]+)[\'"]',
            r'driver\.find_element\s*\(\s*[\'"]xpath[\'"],\s*[\'"]([^\'"]+)[\'"]',
            r'__checkElement\s*\(\s*By\.XPATH\s*,\s*[\'"]([^\'"]+)[\'"]',
            r'WebDriverWait.*?\.until.*?\(\s*By\.XPATH\s*,\s*[\'"]([^\'"]+)[\'"]',
        ]
        
        self.css_patterns = [
            r'By\.CSS_SELECTOR\s*,\s*[\'"]([^\'"]+)[\'"]',
            r'find_element\s*\(\s*By\.CSS_SELECTOR\s*,\s*[\'"]([^\'"]+)[\'"]',
            r'find_elements\s*\(\s*By\.CSS_SELECTOR\s*,\s*[\'"]([^\'"]+)[\'"]',
            r'driver\.find_element\s*\(\s*[\'"]css[\'"],\s*[\'"]([^\'"]+)[\'"]',
            r'__checkElement\s*\(\s*By\.CSS_SELECTOR\s*,\s*[\'"]([^\'"]+)[\'"]',
        ]
        
        self.id_patterns = [
            r'By\.ID\s*,\s*[\'"]([^\'"]+)[\'"]',
            r'find_element\s*\(\s*By\.ID\s*,\s*[\'"]([^\'"]+)[\'"]',
            r'getElementById\s*\(\s*[\'"]([^\'"]+)[\'"]',
        ]
        
        self.class_patterns = [
            r'By\.CLASS_NAME\s*,\s*[\'"]([^\'"]+)[\'"]',
            r'find_element\s*\(\s*By\.CLASS_NAME\s*,\s*[\'"]([^\'"]+)[\'"]',
        ]

    def analyze_file(self, file_path: str) -> Dict:
        """Phân tích một file để tìm các selector"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"Lỗi đọc file {file_path}: {e}")
            return {}
            
        file_selectors = {
            'xpath': [],
            'css': [],
            'id': [],
            'class': []
        }
        
        # Tìm XPath selectors
        for pattern in self.xpath_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                selector = match.strip()
                if selector and len(selector) > 2:  # Bỏ qua selector quá ngắn
                    file_selectors['xpath'].append(selector)
                    self.selector_stats[f"XPATH: {selector}"] += 1
                    self.selector_locations[f"XPATH: {selector}"].append(file_path)
                    self.selector_types['xpath'] += 1
        
        # Tìm CSS selectors
        for pattern in self.css_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                selector = match.strip()
                if selector and len(selector) > 1:
                    file_selectors['css'].append(selector)
                    self.selector_stats[f"CSS: {selector}"] += 1
                    self.selector_locations[f"CSS: {selector}"].append(file_path)
                    self.selector_types['css'] += 1
        
        # Tìm ID selectors
        for pattern in self.id_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                selector = match.strip()
                if selector and len(selector) > 1:
                    file_selectors['id'].append(selector)
                    self.selector_stats[f"ID: {selector}"] += 1
                    self.selector_locations[f"ID: {selector}"].append(file_path)
                    self.selector_types['id'] += 1
        
        # Tìm Class selectors
        for pattern in self.class_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                selector = match.strip()
                if selector and len(selector) > 1:
                    file_selectors['class'].append(selector)
                    self.selector_stats[f"CLASS: {selector}"] += 1
                    self.selector_locations[f"CLASS: {selector}"].append(file_path)
                    self.selector_types['class'] += 1
        
        # Đếm tổng selector trong file
        total_selectors = sum(len(selectors) for selectors in file_selectors.values())
        if total_selectors > 0:
            self.file_stats[file_path] = total_selectors
            
        return file_selectors

    def scan_project(self) -> None:
        """Quét toàn bộ project để tìm selector"""
        print("🔍 Đang quét project để tìm các selector...")
        
        python_files = []
        for root, dirs, files in os.walk(self.project_root):
            # Bỏ qua các thư mục không cần thiết
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', 'runtools.build']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    python_files.append(file_path)
        
        print(f"📁 Tìm thấy {len(python_files)} file Python")
        
        for i, file_path in enumerate(python_files, 1):
            print(f"⏳ Đang phân tích ({i}/{len(python_files)}): {file_path}")
            self.analyze_file(file_path)
        
        print("✅ Hoàn thành quét project!")

    def generate_report(self) -> str:
        """Tạo báo cáo thống kê"""
        report = []
        report.append("=" * 80)
        report.append("📊 BÁO CÁO THỐNG KÊ SELECTOR USAGE")
        report.append("=" * 80)
        report.append(f"🕒 Thời gian tạo báo cáo: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"📂 Thư mục dự án: {os.path.abspath(self.project_root)}")
        report.append("")
        
        # Thống kê tổng quan
        report.append("📈 THỐNG KÊ TỔNG QUAN")
        report.append("-" * 50)
        report.append(f"🎯 Tổng số selector unique: {len(self.selector_stats)}")
        report.append(f"📄 Tổng số file có selector: {len(self.file_stats)}")
        report.append("")
        
        # Thống kê theo loại selector
        report.append("📊 THỐNG KÊ THEO LOẠI SELECTOR")
        report.append("-" * 50)
        for selector_type, count in self.selector_types.items():
            report.append(f"🔸 {selector_type.upper()}: {count} lần sử dụng")
        report.append("")
        
        # Top selector được sử dụng nhiều nhất
        report.append("🏆 TOP 20 SELECTOR ĐƯỢC SỬ DỤNG NHIỀU NHẤT")
        report.append("-" * 50)
        top_selectors = Counter(self.selector_stats).most_common(20)
        for i, (selector, count) in enumerate(top_selectors, 1):
            files = set(self.selector_locations[selector])
            report.append(f"{i:2d}. [{count:2d} lần] {selector}")
            report.append(f"    📁 Trong {len(files)} file: {', '.join(os.path.basename(f) for f in list(files)[:3])}")
            if len(files) > 3:
                report.append(f"    ... và {len(files) - 3} file khác")
            report.append("")
        
        return "\n".join(report)

    def save_detailed_json(self, output_file: str = "selector_analysis.json") -> None:
        """Lưu báo cáo chi tiết dạng JSON"""
        data = {
            "timestamp": datetime.now().isoformat(),
            "project_root": os.path.abspath(self.project_root),
            "summary": {
                "total_unique_selectors": len(self.selector_stats),
                "total_files_with_selectors": len(self.file_stats),
                "selector_types": dict(self.selector_types)
            },
            "top_selectors": [
                {
                    "rank": i + 1,
                    "selector": selector,
                    "usage_count": count,
                    "files": list(set(self.selector_locations[selector]))
                }
                for i, (selector, count) in enumerate(Counter(self.selector_stats).most_common(50))
            ],
            "file_stats": dict(self.file_stats),
            "all_selectors": dict(self.selector_stats)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Đã lưu báo cáo chi tiết vào: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Phân tích selector usage trong AutoChrome-SourceBan")
    parser.add_argument("--project-root", "-p", default=".", help="Đường dẫn thư mục gốc của project")
    parser.add_argument("--output", "-o", default="selector_report.txt", help="File output cho báo cáo")
    parser.add_argument("--json", "-j", default="selector_analysis.json", help="File JSON chi tiết")
    
    args = parser.parse_args()
    
    print("🚀 Bắt đầu phân tích Selector Usage...")
    print(f"📂 Project root: {os.path.abspath(args.project_root)}")
    
    analyzer = SelectorAnalyzer(args.project_root)
    
    # Quét project
    start_time = time.time()
    analyzer.scan_project()
    end_time = time.time()
    
    print(f"⏱️  Thời gian quét: {end_time - start_time:.2f} giây")
    
    # Tạo báo cáo
    report = analyzer.generate_report()
    
    # Lưu báo cáo text
    with open(args.output, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📄 Đã lưu báo cáo vào: {args.output}")
    
    # Lưu báo cáo JSON chi tiết
    analyzer.save_detailed_json(args.json)
    
    # Hiển thị báo cáo ngắn gọn
    print("\n" + "=" * 60)
    print("📋 TÓM TẮT KẾT QUẢ")
    print("=" * 60)
    print(f"🎯 Tổng selector unique: {len(analyzer.selector_stats)}")
    print(f"📄 File có selector: {len(analyzer.file_stats)}")
    
    if analyzer.selector_stats:
        top_3 = Counter(analyzer.selector_stats).most_common(3)
        print("\n🏆 TOP 3 SELECTOR PHỔ BIẾN NHẤT:")
        for i, (selector, count) in enumerate(top_3, 1):
            print(f"  {i}. [{count} lần] {selector[:60]}...")

if __name__ == "__main__":
    main()
