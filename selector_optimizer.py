#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tối ưu selector - T<PERSON><PERSON> chuẩn 1, chuẩn 2, x<PERSON><PERSON> dư thừa
"""

import os
import re
from collections import Counter, defaultdict

class SelectorOptimizer:
    def __init__(self):
        self.all_selectors = []
        self.selector_groups = defaultdict(list)  # Nhóm selector cùng chức năng

    def scan_project(self):
        """Quét project tìm tất cả selector"""
        print("🔍 Đang quét selector...")

        for root, dirs, files in os.walk("."):
            dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
            for file in files:
                if file.endswith('.py'):
                    self._scan_file(os.path.join(root, file))

        print(f"✅ Tìm thấy {len(self.all_selectors)} selector")

    def _scan_file(self, filepath):
        """Quét 1 file"""
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Tìm XPath
            xpaths = re.findall(r'By\.XPATH\s*,\s*[\'"]([^\'"]+)[\'"]', content)
            for xpath in xpaths:
                if len(xpath) > 5:
                    self.all_selectors.append(('XPATH', xpath, filepath))

            # Tìm CSS
            css_selectors = re.findall(r'By\.CSS_SELECTOR\s*,\s*[\'"]([^\'"]+)[\'"]', content)
            for css in css_selectors:
                if len(css) > 2:
                    self.all_selectors.append(('CSS', css, filepath))

            # Tìm ID
            ids = re.findall(r'By\.ID\s*,\s*[\'"]([^\'"]+)[\'"]', content)
            for id_sel in ids:
                self.all_selectors.append(('ID', id_sel, filepath))

        except:
            pass

    def classify_selector_performance(self, selector: str) -> Tuple[str, int]:
        """Phân loại hiệu suất của selector"""
        selector_lower = selector.lower()
        
        # ID selector
        if selector.startswith('ID:') or '#' in selector:
            return 'id', self.fast_selectors['id']
        
        # Class selector
        if selector.startswith('CLASS:') or (selector.startswith('CSS:') and '.' in selector and not '[' in selector):
            return 'class', self.fast_selectors['class']
        
        # Data attribute
        if 'data-e2e' in selector or 'data-' in selector:
            return 'data_attribute', self.fast_selectors['data_attribute']
        
        # CSS với tag + attribute
        if selector.startswith('CSS:') and '[' in selector and not '//' in selector:
            return 'tag_with_attribute', self.fast_selectors['tag_with_attribute']
        
        # CSS đơn giản
        if selector.startswith('CSS:') and not '//' in selector:
            return 'css_simple', self.fast_selectors['css_simple']
        
        # XPath đơn giản
        if selector.startswith('XPATH:') and selector.count('/') <= 3 and not 'contains' in selector:
            return 'xpath_simple', self.fast_selectors['xpath_simple']
        
        # XPath phức tạp
        if selector.startswith('XPATH:'):
            return 'xpath_complex', self.fast_selectors['xpath_complex']
        
        return 'unknown', 1

    def suggest_optimizations(self, selector: str) -> List[Dict]:
        """Đưa ra gợi ý tối ưu cho selector"""
        suggestions = []
        
        if not selector.startswith('XPATH:'):
            return suggestions
        
        xpath = selector[6:].strip()  # Bỏ prefix "XPATH:"
        
        # Kiểm tra các rule tối ưu XPath -> CSS
        for xpath_pattern, css_replacement in self.optimization_rules['xpath_to_css'].items():
            if re.search(xpath_pattern, xpath):
                optimized = re.sub(xpath_pattern, css_replacement, xpath)
                suggestions.append({
                    'type': 'xpath_to_css',
                    'original': selector,
                    'optimized': f'CSS: {optimized}',
                    'reason': 'CSS selector thường nhanh hơn XPath',
                    'performance_gain': 'Trung bình - Cao'
                })
        
        # Kiểm tra XPath phức tạp có thể đơn giản hóa
        if '/html/body/div[' in xpath and xpath.count('/') > 5:
            suggestions.append({
                'type': 'simplify_xpath',
                'original': selector,
                'optimized': 'Sử dụng relative XPath thay vì absolute',
                'reason': 'Absolute XPath dễ bị lỗi khi DOM thay đổi',
                'performance_gain': 'Cao'
            })
        
        # Kiểm tra XPath có contains() có thể tối ưu
        if 'contains(@class,' in xpath and xpath.count('contains') > 1:
            suggestions.append({
                'type': 'optimize_contains',
                'original': selector,
                'optimized': 'Sử dụng CSS class selector thay vì XPath contains',
                'reason': 'CSS class selector nhanh hơn XPath contains',
                'performance_gain': 'Trung bình'
            })
        
        return suggestions

    def analyze_performance_issues(self) -> Dict:
        """Phân tích các vấn đề hiệu suất"""
        issues = {
            'slow_selectors': [],
            'duplicate_selectors': [],
            'complex_selectors': [],
            'optimization_opportunities': []
        }
        
        if 'top_selectors' not in self.analysis_data:
            return issues
        
        # Phân tích từng selector
        for item in self.analysis_data['top_selectors']:
            selector = item['selector']
            usage_count = item['usage_count']
            files = item['files']
            
            # Phân loại hiệu suất
            perf_type, perf_score = self.classify_selector_performance(selector)
            
            # Selector chậm được sử dụng nhiều
            if perf_score <= 3 and usage_count >= 3:
                issues['slow_selectors'].append({
                    'selector': selector,
                    'usage_count': usage_count,
                    'performance_score': perf_score,
                    'files': files,
                    'impact': 'Cao' if usage_count >= 5 else 'Trung bình'
                })
            
            # Selector phức tạp
            if (selector.startswith('XPATH:') and 
                (selector.count('/') > 5 or 'contains' in selector or len(selector) > 100)):
                issues['complex_selectors'].append({
                    'selector': selector,
                    'usage_count': usage_count,
                    'complexity_reason': self._get_complexity_reason(selector),
                    'files': files
                })
            
            # Cơ hội tối ưu hóa
            optimizations = self.suggest_optimizations(selector)
            if optimizations and usage_count >= 2:
                issues['optimization_opportunities'].extend([
                    {**opt, 'usage_count': usage_count, 'files': files}
                    for opt in optimizations
                ])
        
        # Tìm selector trùng lặp (cùng chức năng nhưng khác cú pháp)
        issues['duplicate_selectors'] = self._find_duplicate_selectors()
        
        return issues

    def _get_complexity_reason(self, selector: str) -> str:
        """Xác định lý do selector phức tạp"""
        reasons = []
        if selector.count('/') > 5:
            reasons.append("Quá nhiều level DOM")
        if 'contains' in selector:
            reasons.append("Sử dụng contains()")
        if len(selector) > 100:
            reasons.append("Selector quá dài")
        if '/html/body' in selector:
            reasons.append("Absolute XPath")
        return ", ".join(reasons)

    def _find_duplicate_selectors(self) -> List[Dict]:
        """Tìm các selector có thể trùng lặp chức năng"""
        duplicates = []
        selectors = [item['selector'] for item in self.analysis_data.get('top_selectors', [])]
        
        # Nhóm selector theo data-e2e attribute
        data_e2e_groups = defaultdict(list)
        for selector in selectors:
            if 'data-e2e=' in selector:
                match = re.search(r'data-e2e="([^"]+)"', selector)
                if match:
                    data_e2e_groups[match.group(1)].append(selector)
        
        # Tìm nhóm có nhiều hơn 1 selector
        for attr_value, group_selectors in data_e2e_groups.items():
            if len(group_selectors) > 1:
                duplicates.append({
                    'attribute': f'data-e2e="{attr_value}"',
                    'selectors': group_selectors,
                    'suggestion': f'Chỉ nên sử dụng 1 selector cho data-e2e="{attr_value}"'
                })
        
        return duplicates

    def generate_optimization_report(self) -> str:
        """Tạo báo cáo tối ưu hóa"""
        issues = self.analyze_performance_issues()
        
        report = []
        report.append("=" * 80)
        report.append("🚀 BÁO CÁO TỐI ƯU HÓA SELECTOR")
        report.append("=" * 80)
        report.append(f"🕒 Thời gian: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Tóm tắt vấn đề
        report.append("📊 TÓM TẮT VẤN ĐỀ")
        report.append("-" * 50)
        report.append(f"🐌 Selector chậm: {len(issues['slow_selectors'])}")
        report.append(f"🔄 Selector trùng lặp: {len(issues['duplicate_selectors'])}")
        report.append(f"🧩 Selector phức tạp: {len(issues['complex_selectors'])}")
        report.append(f"💡 Cơ hội tối ưu: {len(issues['optimization_opportunities'])}")
        report.append("")
        
        # Selector chậm cần ưu tiên
        if issues['slow_selectors']:
            report.append("🚨 SELECTOR CHẬM CẦN ƯU TIÊN TỐI ƯU")
            report.append("-" * 50)
            for i, issue in enumerate(issues['slow_selectors'][:10], 1):
                report.append(f"{i}. [{issue['usage_count']} lần] {issue['selector'][:70]}...")
                report.append(f"   📈 Điểm hiệu suất: {issue['performance_score']}/10")
                report.append(f"   🎯 Mức độ ảnh hưởng: {issue['impact']}")
                report.append(f"   📁 Trong {len(issue['files'])} file")
                report.append("")
        
        # Gợi ý tối ưu hóa
        if issues['optimization_opportunities']:
            report.append("💡 GỢI Ý TỐI ƯU HÓA")
            report.append("-" * 50)
            for i, opt in enumerate(issues['optimization_opportunities'][:10], 1):
                report.append(f"{i}. {opt['type'].upper()}")
                report.append(f"   🔍 Hiện tại: {opt['original'][:60]}...")
                report.append(f"   ✨ Tối ưu: {opt['optimized'][:60]}...")
                report.append(f"   📈 Lợi ích: {opt['performance_gain']}")
                report.append(f"   💬 Lý do: {opt['reason']}")
                report.append(f"   📊 Sử dụng: {opt['usage_count']} lần")
                report.append("")
        
        # Selector trùng lặp
        if issues['duplicate_selectors']:
            report.append("🔄 SELECTOR TRÙNG LẶP")
            report.append("-" * 50)
            for i, dup in enumerate(issues['duplicate_selectors'][:5], 1):
                report.append(f"{i}. {dup['attribute']}")
                report.append(f"   🔍 Các selector: {len(dup['selectors'])} variant")
                for sel in dup['selectors']:
                    report.append(f"      - {sel[:60]}...")
                report.append(f"   💡 {dup['suggestion']}")
                report.append("")
        
        return "\n".join(report)

    def save_optimization_suggestions(self, output_file: str = "selector_optimizations.json"):
        """Lưu gợi ý tối ưu hóa dạng JSON"""
        issues = self.analyze_performance_issues()
        
        data = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "slow_selectors_count": len(issues['slow_selectors']),
                "duplicate_selectors_count": len(issues['duplicate_selectors']),
                "complex_selectors_count": len(issues['complex_selectors']),
                "optimization_opportunities_count": len(issues['optimization_opportunities'])
            },
            "issues": issues,
            "recommendations": self._generate_recommendations(issues)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Đã lưu gợi ý tối ưu hóa vào: {output_file}")

    def _generate_recommendations(self, issues: Dict) -> List[Dict]:
        """Tạo danh sách khuyến nghị ưu tiên"""
        recommendations = []
        
        # Ưu tiên cao: Selector chậm được sử dụng nhiều
        for issue in issues['slow_selectors']:
            if issue['usage_count'] >= 5:
                recommendations.append({
                    'priority': 'Cao',
                    'type': 'performance',
                    'description': f"Tối ưu selector được sử dụng {issue['usage_count']} lần",
                    'selector': issue['selector'],
                    'action': 'Chuyển sang CSS selector hoặc sử dụng ID/class'
                })
        
        # Ưu tiên trung bình: Cơ hội tối ưu hóa
        for opt in issues['optimization_opportunities']:
            if opt['usage_count'] >= 3:
                recommendations.append({
                    'priority': 'Trung bình',
                    'type': 'optimization',
                    'description': f"Tối ưu {opt['type']} được sử dụng {opt['usage_count']} lần",
                    'selector': opt['original'],
                    'action': opt['optimized']
                })
        
        return sorted(recommendations, key=lambda x: {'Cao': 3, 'Trung bình': 2, 'Thấp': 1}[x['priority']], reverse=True)

def main():
    parser = argparse.ArgumentParser(description="Tối ưu hóa selector trong AutoChrome-SourceBan")
    parser.add_argument("--analysis", "-a", default="selector_analysis.json", help="File phân tích selector")
    parser.add_argument("--output", "-o", default="optimization_report.txt", help="File báo cáo tối ưu")
    parser.add_argument("--json", "-j", default="selector_optimizations.json", help="File JSON gợi ý tối ưu")
    
    args = parser.parse_args()
    
    print("🚀 Bắt đầu phân tích tối ưu hóa selector...")
    
    optimizer = SelectorOptimizer(args.analysis)
    
    # Tạo báo cáo tối ưu hóa
    report = optimizer.generate_optimization_report()
    
    # Lưu báo cáo
    with open(args.output, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📄 Đã lưu báo cáo tối ưu vào: {args.output}")
    
    # Lưu gợi ý JSON
    optimizer.save_optimization_suggestions(args.json)
    
    # Hiển thị tóm tắt
    issues = optimizer.analyze_performance_issues()
    print("\n" + "=" * 60)
    print("📋 TÓM TẮT KẾT QUẢ TỐI ƯU HÓA")
    print("=" * 60)
    print(f"🐌 Selector chậm cần tối ưu: {len(issues['slow_selectors'])}")
    print(f"💡 Cơ hội tối ưu hóa: {len(issues['optimization_opportunities'])}")
    print(f"🔄 Selector trùng lặp: {len(issues['duplicate_selectors'])}")

if __name__ == "__main__":
    main()
