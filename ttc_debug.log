﻿[2025-07-14 11:31:22.091773] TTC getJob called for type: comment
[2025-07-14 11:31:22.091773] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=d33hqe5bl8mgtnp16e23v7veo4; Path=/'}
[2025-07-14 11:31:22.092272] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 11:31:22.265919] Request successful, status: 200
[2025-07-14 11:31:22.266421] TTC comment response status: 200
[2025-07-14 11:31:22.266421] TTC comment response text: [{"idpost":"7525297318116085000","link":"https:\/\/www.tiktok.com\/@mun173854653\/video\/7525297318116085000","nd":"[\"B\\u1ea1n c\\u00f3 chia s\\u1ebb m\\u1eb9o l\\u00e0m dropshipping kh\\u00f4ng?\",\"B\\u1ea1n d\\u00f9ng m\\u1eb9o l\\u00e0m dropshipping n\\u00e0o \\u0111\\u1ec3 ki\\u1ebfm \\u0111\\u01b0\\u1ee3c nh\\u01b0 n\\u00e0y \\u1ea1?\",\"Nh\\u00ecn hoa h\\u1ed3ng m\\u00e0 mu\\u1ed1n th\\u1eed s\\u1ee9c li\\u1ec1n!\",\"L\\u00e0m dropshipping nh\\u01b0 b\\u1ea1n c\\u00f3 kh\\u00f3 kh\\u00f...
[2025-07-14 11:31:22.267918] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 11:31:22.268414] TTC comment - Jobs list detected, length: 36
[2025-07-14 11:31:22.310334] TTC starting jobs iteration: 36 jobs
[2025-07-14 11:31:22.310836] TTC comment settings: max=12345, cache=2
[2025-07-14 11:31:22.311334] TTC processing job 1: 7525297318116085000, https://www.tiktok.com/@mun173854653/video/7525297318116085000
[2025-07-14 11:31:22.311833] TTC comment content found: ["B\u1ea1n c\u00f3 chia s\u1ebb m\u1eb9o l\u00e0m dropshipping kh\u00f4ng?","B\u1ea1n d\u00f9ng m\u1...
[2025-07-14 11:31:22.312840] TTC navigating to: https://www.tiktok.com/@mun173854653/video/7525297318116085000
[2025-07-14 11:31:23.976946] TTC navigation successful
[2025-07-14 11:31:23.977959] TTC calling __clickXmlJobs with comment: True
[2025-07-14 11:32:03.532175] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 11:32:08.584187] TTC trying selector: div[data-e2e="comment-input"] div[role="textbox"]
